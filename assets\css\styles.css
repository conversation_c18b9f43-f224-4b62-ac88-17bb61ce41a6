/* ===== CSS RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
    /* Colors - Premium Dark Theme */
    --primary-50: #f0f9ff;
    --primary-100: #e0f2fe;
    --primary-200: #bae6fd;
    --primary-300: #7dd3fc;
    --primary-400: #38bdf8;
    --primary-500: #0ea5e9;
    --primary-600: #0284c7;
    --primary-700: #0369a1;
    --primary-800: #075985;
    --primary-900: #0c4a6e;
    
    --accent-400: #f59e0b;
    --accent-500: #d97706;
    --accent-600: #b45309;
    
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* Semantic Colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-400) 0%, var(--accent-600) 100%);
    --gradient-text: linear-gradient(135deg, var(--primary-400) 0%, var(--accent-400) 100%);
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    --space-3xl: 4rem;
    --space-4xl: 6rem;
    
    /* Typography */
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-size-6xl: 3.75rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--space-lg);
}

.text-gradient {
    background: var(--gradient-text);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(203, 213, 225, 0.1);
    transition: var(--transition-normal);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--space-md) var(--space-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 700;
    font-size: var(--font-size-xl);
}

.brand-logo {
    width: 32px;
    height: 32px;
    margin-right: var(--space-sm);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--space-2xl);
}

.nav-list {
    display: flex;
    list-style: none;
    gap: var(--space-xl);
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--text-primary);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    gap: var(--space-md);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger-line {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition-fast);
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-outline {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid var(--gray-600);
}

.btn-outline:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--gray-500);
}

.btn-large {
    padding: var(--space-md) var(--space-xl);
    font-size: var(--font-size-base);
}

.btn-icon {
    width: 16px;
    height: 16px;
}

/* ===== HERO SECTION ===== */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    inset: 0;
    z-index: -1;
}

.hero-gradient {
    position: absolute;
    inset: 0;
    background: radial-gradient(ellipse at center, rgba(14, 165, 233, 0.15) 0%, transparent 70%);
}

.hero-pattern {
    position: absolute;
    inset: 0;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(245, 158, 11, 0.1) 0%, transparent 50%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--space-4xl);
    align-items: center;
    padding: var(--space-4xl) 0;
}

.hero-title {
    font-size: var(--font-size-5xl);
    font-weight: 800;
    line-height: 1.1;
    margin-bottom: var(--space-lg);
}

.hero-description {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--space-2xl);
    line-height: 1.6;
}

.hero-stats {
    display: flex;
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-400);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

.hero-actions {
    display: flex;
    gap: var(--space-lg);
    flex-wrap: wrap;
}

.hero-visual {
    position: relative;
}

.hero-image-container {
    position: relative;
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--shadow-2xl);
}

.hero-image {
    width: 100%;
    height: auto;
    display: block;
}

.hero-floating-cards {
    position: absolute;
    inset: 0;
}

.floating-card {
    position: absolute;
    background: rgba(15, 23, 42, 0.9);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(203, 213, 225, 0.1);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    box-shadow: var(--shadow-xl);
    animation: float 6s ease-in-out infinite;
}

.card-1 {
    top: 20%;
    right: -10%;
    animation-delay: 0s;
}

.card-2 {
    bottom: 30%;
    left: -15%;
    animation-delay: 2s;
}

.card-3 {
    top: 60%;
    right: -5%;
    animation-delay: 4s;
}

.card-icon {
    font-size: var(--font-size-lg);
}

.card-text {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--text-primary);
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* ===== TRUST SECTION ===== */
.trust-section {
    padding: var(--space-2xl) 0;
    border-bottom: 1px solid var(--gray-800);
}

.trust-content {
    text-align: center;
}

.trust-label {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-lg);
}

.trust-logos {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--space-2xl);
    flex-wrap: wrap;
}

.trust-logo {
    height: 32px;
    opacity: 0.6;
    transition: var(--transition-fast);
}

.trust-logo:hover {
    opacity: 1;
}

/* ===== VALUE PROPOSITION ===== */
.value-proposition {
    padding: var(--space-4xl) 0;
}

.section-header {
    text-align: center;
    margin-bottom: var(--space-4xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--space-lg);
}

.section-description {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-2xl);
}

.feature-card {
    background: var(--bg-secondary);
    border: 1px solid var(--gray-700);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    text-align: center;
    transition: var(--transition-normal);
}

.feature-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-600);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto var(--space-lg);
    background: var(--gradient-primary);
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.feature-icon svg {
    width: 32px;
    height: 32px;
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-md);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background: var(--bg-primary);
        border-top: 1px solid var(--gray-700);
        padding: var(--space-xl);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
        z-index: 999;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-list {
        flex-direction: column;
        gap: var(--space-lg);
        margin-bottom: var(--space-xl);
    }

    .nav-actions {
        flex-direction: column;
        gap: var(--space-md);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .hamburger-line:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .hamburger-line:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .hamburger-line:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    /* Hero Section Mobile */
    .hero-content {
        grid-template-columns: 1fr;
        gap: var(--space-2xl);
        text-align: center;
        padding: var(--space-2xl) 0;
    }

    .hero-title {
        font-size: var(--font-size-4xl);
        line-height: 1.2;
    }

    .hero-description {
        font-size: var(--font-size-lg);
    }

    .hero-stats {
        justify-content: center;
        gap: var(--space-lg);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-floating-cards {
        display: none; /* Hide floating cards on mobile for performance */
    }

    /* Grid Layouts Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--space-xl);
    }

    .trust-logos {
        gap: var(--space-lg);
        flex-wrap: wrap;
    }

    .trust-logo {
        height: 24px;
    }

    /* Container Padding */
    .container {
        padding: 0 var(--space-md);
    }

    /* Section Spacing */
    .value-proposition,
    .products-section,
    .tutorials-section,
    .projects-section,
    .testimonials-section {
        padding: var(--space-2xl) 0;
    }

    .section-title {
        font-size: var(--font-size-3xl);
    }

    .section-description {
        font-size: var(--font-size-lg);
    }
}

/* Tablet Responsive */
@media (max-width: 1024px) and (min-width: 769px) {
    .hero-content {
        gap: var(--space-3xl);
    }

    .hero-title {
        font-size: var(--font-size-5xl);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .container {
        padding: 0 var(--space-lg);
    }
}

/* Large Screen Optimizations */
@media (min-width: 1400px) {
    .container {
        max-width: 1400px;
    }

    .hero-title {
        font-size: var(--font-size-6xl);
    }

    .section-title {
        font-size: var(--font-size-5xl);
    }
}

/* Performance Optimizations */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .floating-card {
        animation: none;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-image,
    .product-image,
    .tutorial-thumbnail,
    .project-image {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Dark Mode Support (if system preference) */
@media (prefers-color-scheme: dark) {
    /* Already using dark theme, but can add overrides here */
}

/* Print Styles */
@media print {
    .navbar,
    .nav-toggle,
    .hero-floating-cards,
    .cta-section,
    .footer {
        display: none;
    }

    .hero-background {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .section-title,
    .hero-title {
        color: black;
    }
}
