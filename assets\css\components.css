/* ===== PRODUCT SHOWCASE COMPONENTS ===== */
.products-section {
    padding: var(--space-4xl) 0;
    background: var(--bg-secondary);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-2xl);
    margin-top: var(--space-3xl);
}

.product-card {
    background: var(--bg-primary);
    border: 1px solid var(--gray-700);
    border-radius: var(--radius-xl);
    overflow: hidden;
    transition: var(--transition-normal);
    position: relative;
}

.product-card:hover {
    transform: translateY(-8px);
    border-color: var(--primary-600);
    box-shadow: var(--shadow-2xl);
}

.product-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    background: linear-gradient(135deg, var(--gray-800) 0%, var(--gray-700) 100%);
}

.product-content {
    padding: var(--space-xl);
}

.product-badge {
    position: absolute;
    top: var(--space-md);
    right: var(--space-md);
    background: var(--gradient-accent);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
}

.product-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.product-description {
    color: var(--text-secondary);
    margin-bottom: var(--space-lg);
    line-height: 1.6;
}

.product-features {
    list-style: none;
    margin-bottom: var(--space-lg);
}

.product-features li {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-sm);
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.product-features li::before {
    content: '✓';
    color: var(--primary-400);
    font-weight: bold;
}

.product-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-lg);
    border-top: 1px solid var(--gray-700);
}

.product-price {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-400);
}

.product-price-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    display: block;
}

/* ===== TUTORIAL MARKETPLACE COMPONENTS ===== */
.tutorials-section {
    padding: var(--space-4xl) 0;
}

.tutorial-filters {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-2xl);
    flex-wrap: wrap;
}

.filter-btn {
    padding: var(--space-sm) var(--space-lg);
    background: transparent;
    border: 1px solid var(--gray-600);
    border-radius: var(--radius-md);
    color: var(--text-secondary);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.filter-btn.active,
.filter-btn:hover {
    background: var(--primary-600);
    border-color: var(--primary-600);
    color: white;
}

.tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-xl);
}

.tutorial-card {
    background: var(--bg-secondary);
    border: 1px solid var(--gray-700);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: var(--transition-normal);
}

.tutorial-card:hover {
    transform: translateY(-4px);
    border-color: var(--primary-600);
    box-shadow: var(--shadow-lg);
}

.tutorial-thumbnail {
    width: 100%;
    height: 180px;
    object-fit: cover;
    background: var(--gray-800);
}

.tutorial-info {
    padding: var(--space-lg);
}

.tutorial-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.tutorial-level {
    background: var(--primary-600);
    color: white;
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
}

.tutorial-duration {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.tutorial-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.tutorial-excerpt {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.tutorial-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tutorial-price {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--accent-400);
}

.tutorial-rating {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.stars {
    color: var(--accent-400);
}

/* ===== PROJECT GALLERY COMPONENTS ===== */
.projects-section {
    padding: var(--space-4xl) 0;
    background: var(--bg-secondary);
}

.project-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--space-2xl);
    margin-top: var(--space-3xl);
}

.project-card {
    background: var(--bg-primary);
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.project-card:hover {
    transform: scale(1.02);
    box-shadow: var(--shadow-2xl);
}

.project-media {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.project-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.project-card:hover .project-image {
    transform: scale(1.1);
}

.project-overlay {
    position: absolute;
    inset: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, transparent 50%);
    display: flex;
    align-items: flex-end;
    padding: var(--space-lg);
}

.project-tags {
    display: flex;
    gap: var(--space-sm);
    flex-wrap: wrap;
}

.project-tag {
    background: rgba(14, 165, 233, 0.2);
    border: 1px solid var(--primary-600);
    color: var(--primary-300);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.project-details {
    padding: var(--space-xl);
}

.project-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--space-sm);
}

.project-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
}

.project-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--space-lg);
    border-top: 1px solid var(--gray-700);
}

.project-complexity {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.complexity-dots {
    display: flex;
    gap: 2px;
}

.complexity-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--gray-600);
}

.complexity-dot.active {
    background: var(--accent-400);
}

.project-link {
    color: var(--primary-400);
    text-decoration: none;
    font-weight: 500;
    font-size: var(--font-size-sm);
    transition: var(--transition-fast);
}

.project-link:hover {
    color: var(--primary-300);
}

/* ===== TESTIMONIALS COMPONENT ===== */
.testimonials-section {
    padding: var(--space-4xl) 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--space-xl);
    margin-top: var(--space-3xl);
}

.testimonial-card {
    background: var(--bg-secondary);
    border: 1px solid var(--gray-700);
    border-radius: var(--radius-lg);
    padding: var(--space-xl);
    position: relative;
}

.testimonial-quote {
    font-size: var(--font-size-lg);
    line-height: 1.6;
    margin-bottom: var(--space-lg);
    color: var(--text-secondary);
}

.testimonial-quote::before {
    content: '"';
    font-size: var(--font-size-3xl);
    color: var(--primary-400);
    position: absolute;
    top: var(--space-md);
    left: var(--space-md);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.author-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info h4 {
    font-weight: 600;
    margin-bottom: var(--space-xs);
}

.author-title {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* ===== CTA SECTION ===== */
.cta-section {
    padding: var(--space-4xl) 0;
    background: var(--gradient-primary);
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--space-lg);
    color: white;
}

.cta-description {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-2xl);
    color: rgba(255, 255, 255, 0.9);
}

.cta-actions {
    display: flex;
    gap: var(--space-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.btn-white {
    background: white;
    color: var(--primary-700);
    border: none;
}

.btn-white:hover {
    background: var(--gray-100);
    transform: translateY(-2px);
}

/* ===== FOOTER ===== */
.footer {
    background: var(--bg-primary);
    border-top: 1px solid var(--gray-800);
    padding: var(--space-4xl) 0 var(--space-xl);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-2xl);
    margin-bottom: var(--space-2xl);
}

.footer-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--space-lg);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--space-sm);
}

.footer-links a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--primary-400);
}

.footer-bottom {
    text-align: center;
    padding-top: var(--space-xl);
    border-top: 1px solid var(--gray-800);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .product-grid,
    .tutorial-grid,
    .project-showcase {
        grid-template-columns: 1fr;
    }
    
    .tutorial-filters {
        justify-content: center;
    }
    
    .cta-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .project-showcase {
        grid-template-columns: 1fr;
    }
    
    .testimonials-grid {
        grid-template-columns: 1fr;
    }
}
