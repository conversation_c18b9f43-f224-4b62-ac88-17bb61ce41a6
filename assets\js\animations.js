// ===== ADVANCED ANIMATIONS =====

// Animation utilities and advanced effects
class AnimationController {
    constructor() {
        this.observers = new Map();
        this.animations = new Map();
        this.init();
    }
    
    init() {
        this.setupIntersectionObservers();
        this.initializeScrollAnimations();
        this.setupParallaxEffects();
        this.initializeHoverEffects();
        this.setupLoadingAnimations();
    }
    
    // ===== INTERSECTION OBSERVERS =====
    setupIntersectionObservers() {
        // Fade in animation observer
        const fadeInObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-active');
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        // Slide up animation observer
        const slideUpObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('slide-up-active');
                }
            });
        }, {
            threshold: 0.2,
            rootMargin: '0px 0px -30px 0px'
        });
        
        // Scale animation observer
        const scaleObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('scale-in-active');
                }
            });
        }, {
            threshold: 0.3
        });
        
        // Apply observers to elements
        document.querySelectorAll('.fade-in').forEach(el => fadeInObserver.observe(el));
        document.querySelectorAll('.slide-up').forEach(el => slideUpObserver.observe(el));
        document.querySelectorAll('.scale-in').forEach(el => scaleObserver.observe(el));
        
        this.observers.set('fadeIn', fadeInObserver);
        this.observers.set('slideUp', slideUpObserver);
        this.observers.set('scale', scaleObserver);
    }
    
    // ===== SCROLL ANIMATIONS =====
    initializeScrollAnimations() {
        let ticking = false;
        
        const updateScrollAnimations = () => {
            const scrollY = window.pageYOffset;
            const windowHeight = window.innerHeight;
            
            // Parallax backgrounds
            this.updateParallaxElements(scrollY);
            
            // Progress indicators
            this.updateProgressIndicators(scrollY);
            
            // Floating elements
            this.updateFloatingElements(scrollY);
            
            ticking = false;
        };
        
        const requestScrollUpdate = () => {
            if (!ticking) {
                requestAnimationFrame(updateScrollAnimations);
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', requestScrollUpdate, { passive: true });
    }
    
    updateParallaxElements(scrollY) {
        const parallaxElements = document.querySelectorAll('[data-parallax]');
        
        parallaxElements.forEach(element => {
            const speed = parseFloat(element.dataset.parallax) || 0.5;
            const yPos = -(scrollY * speed);
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    updateProgressIndicators(scrollY) {
        const progressBars = document.querySelectorAll('.progress-bar');
        const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
        const progress = (scrollY / documentHeight) * 100;
        
        progressBars.forEach(bar => {
            bar.style.width = `${Math.min(progress, 100)}%`;
        });
    }
    
    updateFloatingElements(scrollY) {
        const floatingElements = document.querySelectorAll('.floating-element');
        
        floatingElements.forEach((element, index) => {
            const speed = 0.5 + (index * 0.1);
            const yPos = Math.sin(scrollY * 0.01 + index) * 10;
            element.style.transform = `translateY(${yPos}px)`;
        });
    }
    
    // ===== PARALLAX EFFECTS =====
    setupParallaxEffects() {
        const parallaxSections = document.querySelectorAll('.parallax-section');
        
        parallaxSections.forEach(section => {
            const background = section.querySelector('.parallax-bg');
            if (background) {
                background.dataset.parallax = '0.3';
            }
        });
    }
    
    // ===== HOVER EFFECTS =====
    initializeHoverEffects() {
        // 3D tilt effect for cards
        const tiltCards = document.querySelectorAll('.tilt-card');
        
        tiltCards.forEach(card => {
            card.addEventListener('mousemove', (e) => {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale3d(1.05, 1.05, 1.05)`;
            });
            
            card.addEventListener('mouseleave', () => {
                card.style.transform = 'perspective(1000px) rotateX(0deg) rotateY(0deg) scale3d(1, 1, 1)';
            });
        });
        
        // Magnetic effect for buttons
        const magneticButtons = document.querySelectorAll('.magnetic-btn');
        
        magneticButtons.forEach(button => {
            button.addEventListener('mousemove', (e) => {
                const rect = button.getBoundingClientRect();
                const x = e.clientX - rect.left - rect.width / 2;
                const y = e.clientY - rect.top - rect.height / 2;
                
                button.style.transform = `translate(${x * 0.3}px, ${y * 0.3}px)`;
            });
            
            button.addEventListener('mouseleave', () => {
                button.style.transform = 'translate(0px, 0px)';
            });
        });
    }
    
    // ===== LOADING ANIMATIONS =====
    setupLoadingAnimations() {
        // Skeleton loading for images
        const images = document.querySelectorAll('img[data-src]');
        
        images.forEach(img => {
            img.classList.add('skeleton-loading');
            
            img.addEventListener('load', () => {
                img.classList.remove('skeleton-loading');
                img.classList.add('image-loaded');
            });
        });
        
        // Stagger animation for grids
        this.staggerGridItems();
    }
    
    staggerGridItems() {
        const grids = document.querySelectorAll('.stagger-grid');
        
        grids.forEach(grid => {
            const items = grid.querySelectorAll('.grid-item');
            
            items.forEach((item, index) => {
                item.style.animationDelay = `${index * 0.1}s`;
                item.classList.add('stagger-item');
            });
        });
    }
    
    // ===== UTILITY METHODS =====
    
    // Animate number counting
    animateNumber(element, start, end, duration = 2000) {
        const startTime = performance.now();
        const difference = end - start;
        
        const step = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // Easing function (ease-out)
            const easeOut = 1 - Math.pow(1 - progress, 3);
            const current = start + (difference * easeOut);
            
            element.textContent = Math.floor(current).toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(step);
            } else {
                element.textContent = end.toLocaleString();
            }
        };
        
        requestAnimationFrame(step);
    }
    
    // Typewriter effect
    typeWriter(element, text, speed = 50) {
        element.textContent = '';
        let i = 0;
        
        const type = () => {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        };
        
        type();
    }
    
    // Morphing shapes animation
    morphShape(element, paths, duration = 1000) {
        if (!element || !paths.length) return;
        
        let currentIndex = 0;
        
        const morph = () => {
            const nextIndex = (currentIndex + 1) % paths.length;
            
            element.style.transition = `d ${duration}ms ease-in-out`;
            element.setAttribute('d', paths[nextIndex]);
            
            currentIndex = nextIndex;
        };
        
        setInterval(morph, duration + 500);
    }
    
    // Particle system
    createParticleSystem(container, options = {}) {
        const defaults = {
            particleCount: 50,
            particleSize: 2,
            particleColor: '#3b82f6',
            speed: 1,
            direction: 'up'
        };
        
        const config = { ...defaults, ...options };
        
        for (let i = 0; i < config.particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            Object.assign(particle.style, {
                position: 'absolute',
                width: `${config.particleSize}px`,
                height: `${config.particleSize}px`,
                backgroundColor: config.particleColor,
                borderRadius: '50%',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `particle-float-${config.direction} ${5 + Math.random() * 10}s linear infinite`,
                animationDelay: `${Math.random() * 5}s`
            });
            
            container.appendChild(particle);
        }
    }
    
    // Cleanup method
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
        this.animations.clear();
    }
}

// ===== CSS ANIMATIONS (to be added to CSS) =====
const animationStyles = `
    /* Fade in animation */
    .fade-in {
        opacity: 0;
        transition: opacity 0.6s ease-out;
    }
    
    .fade-in-active {
        opacity: 1;
    }
    
    /* Slide up animation */
    .slide-up {
        opacity: 0;
        transform: translateY(30px);
        transition: opacity 0.6s ease-out, transform 0.6s ease-out;
    }
    
    .slide-up-active {
        opacity: 1;
        transform: translateY(0);
    }
    
    /* Scale in animation */
    .scale-in {
        opacity: 0;
        transform: scale(0.8);
        transition: opacity 0.5s ease-out, transform 0.5s ease-out;
    }
    
    .scale-in-active {
        opacity: 1;
        transform: scale(1);
    }
    
    /* Skeleton loading */
    .skeleton-loading {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: skeleton-pulse 1.5s infinite;
    }
    
    @keyframes skeleton-pulse {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    
    /* Image loaded animation */
    .image-loaded {
        animation: image-reveal 0.5s ease-out;
    }
    
    @keyframes image-reveal {
        from { opacity: 0; transform: scale(1.1); }
        to { opacity: 1; transform: scale(1); }
    }
    
    /* Stagger grid animation */
    .stagger-item {
        opacity: 0;
        transform: translateY(20px);
        animation: stagger-in 0.6s ease-out forwards;
    }
    
    @keyframes stagger-in {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    /* Particle animations */
    @keyframes particle-float-up {
        from {
            transform: translateY(100vh) rotate(0deg);
            opacity: 1;
        }
        to {
            transform: translateY(-100px) rotate(360deg);
            opacity: 0;
        }
    }
    
    /* Tilt card transition */
    .tilt-card {
        transition: transform 0.3s ease-out;
    }
    
    /* Magnetic button transition */
    .magnetic-btn {
        transition: transform 0.3s ease-out;
    }
`;

// Inject animation styles
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

// Initialize animation controller when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.animationController = new AnimationController();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimationController;
}
