# Deployment & Integration Guide

This guide covers deployment options, e-commerce integration, and customization for the IoTech Academy website.

## 🚀 Deployment Options

### 1. Static Hosting (Recommended for <PERSON>)

#### Netlify (Free Tier Available)
```bash
# 1. Install Netlify CLI
npm install -g netlify-cli

# 2. Build and deploy
netlify deploy --prod --dir .
```

#### Vercel (Free Tier Available)
```bash
# 1. Install Vercel CLI
npm install -g vercel

# 2. Deploy
vercel --prod
```

#### GitHub Pages (Free)
1. Push code to GitHub repository
2. Go to Settings > Pages
3. Select source branch (main)
4. Site will be available at `https://username.github.io/repository-name`

#### AWS S3 + CloudFront
```bash
# 1. Upload files to S3 bucket
aws s3 sync . s3://your-bucket-name --exclude "*.md" --exclude ".git/*"

# 2. Configure CloudFront distribution
# 3. Set up custom domain with Route 53
```

### 2. Dynamic Hosting (For Backend Integration)

#### Heroku
```bash
# 1. Create Procfile
echo "web: python -m http.server \$PORT" > Procfile

# 2. Deploy
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

#### DigitalOcean App Platform
1. Connect GitHub repository
2. Configure build settings
3. Deploy automatically on push

## 💳 E-commerce Integration

### 1. Stripe Integration (Recommended)

#### Setup
```html
<!-- Add to head section -->
<script src="https://js.stripe.com/v3/"></script>
```

#### Product Purchase Implementation
```javascript
// Add to assets/js/main.js
const stripe = Stripe('pk_test_your_publishable_key');

function initializeStripe() {
    const buyButtons = document.querySelectorAll('.btn-primary');
    
    buyButtons.forEach(button => {
        button.addEventListener('click', async (e) => {
            e.preventDefault();
            
            const productId = button.dataset.productId;
            const price = button.dataset.price;
            
            try {
                const response = await fetch('/create-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        productId: productId,
                        price: price
                    })
                });
                
                const session = await response.json();
                
                const result = await stripe.redirectToCheckout({
                    sessionId: session.id
                });
                
                if (result.error) {
                    console.error(result.error.message);
                }
            } catch (error) {
                console.error('Error:', error);
            }
        });
    });
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', initializeStripe);
```

#### Backend Implementation (Node.js/Express)
```javascript
const express = require('express');
const stripe = require('stripe')('sk_test_your_secret_key');
const app = express();

app.use(express.json());

app.post('/create-checkout-session', async (req, res) => {
    const { productId, price } = req.body;
    
    try {
        const session = await stripe.checkout.sessions.create({
            payment_method_types: ['card'],
            line_items: [{
                price_data: {
                    currency: 'usd',
                    product_data: {
                        name: 'IoT Development Kit',
                    },
                    unit_amount: price * 100, // Convert to cents
                },
                quantity: 1,
            }],
            mode: 'payment',
            success_url: 'https://yoursite.com/success',
            cancel_url: 'https://yoursite.com/cancel',
        });
        
        res.json({ id: session.id });
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(3000);
```

### 2. PayPal Integration

#### Setup
```html
<!-- Add to head section -->
<script src="https://www.paypal.com/sdk/js?client-id=YOUR_CLIENT_ID"></script>
```

#### Implementation
```javascript
function initializePayPal() {
    paypal.Buttons({
        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    amount: {
                        value: '299.00'
                    }
                }]
            });
        },
        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                alert('Transaction completed by ' + details.payer.name.given_name);
            });
        }
    }).render('#paypal-button-container');
}
```

### 3. Shopify Buy Button

```html
<!-- Add where you want the buy button -->
<div id="product-component-1"></div>

<script>
(function () {
    var scriptURL = 'https://sdks.shopifycdn.com/buy-button/latest/buy-button-storefront.min.js';
    
    var script = document.createElement('script');
    script.async = true;
    script.src = scriptURL;
    
    (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(script);
    
    script.onload = ShopifyBuyInit;
    
    function ShopifyBuyInit() {
        var client = ShopifyBuy.buildClient({
            domain: 'your-shop-name.myshopify.com',
            storefrontAccessToken: 'your-storefront-access-token'
        });
        
        ShopifyBuy.UI.onReady(client).then(function (ui) {
            ui.createComponent('product', {
                id: 'your-product-id',
                node: document.getElementById('product-component-1'),
                options: {
                    product: {
                        styles: {
                            button: {
                                background: 'linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%)',
                                'border-radius': '8px'
                            }
                        }
                    }
                }
            });
        });
    }
})();
</script>
```

## 📚 Content Management Integration

### 1. Headless CMS (Strapi)

#### Setup
```bash
# Install Strapi
npx create-strapi-app iotech-cms --quickstart

# Create content types for:
# - Products
# - Tutorials
# - Projects
# - Testimonials
```

#### API Integration
```javascript
// Fetch products from Strapi
async function loadProducts() {
    try {
        const response = await fetch('http://localhost:1337/api/products?populate=*');
        const data = await response.json();
        
        const productGrid = document.querySelector('.product-grid');
        productGrid.innerHTML = '';
        
        data.data.forEach(product => {
            const productCard = createProductCard(product.attributes);
            productGrid.appendChild(productCard);
        });
    } catch (error) {
        console.error('Error loading products:', error);
    }
}

function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    card.innerHTML = `
        <img src="${product.image.data.attributes.url}" alt="${product.title}" class="product-image">
        <div class="product-content">
            <h3 class="product-title">${product.title}</h3>
            <p class="product-description">${product.description}</p>
            <div class="product-footer">
                <div class="product-price">$${product.price}</div>
                <button class="btn btn-primary" data-product-id="${product.id}">Add to Cart</button>
            </div>
        </div>
    `;
    return card;
}
```

### 2. WordPress Integration

#### Custom Post Types
```php
// Add to functions.php
function create_iot_post_types() {
    // Products
    register_post_type('products', array(
        'public' => true,
        'label' => 'Products',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'show_in_rest' => true
    ));
    
    // Tutorials
    register_post_type('tutorials', array(
        'public' => true,
        'label' => 'Tutorials',
        'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
        'show_in_rest' => true
    ));
}
add_action('init', 'create_iot_post_types');
```

#### REST API Integration
```javascript
// Fetch from WordPress REST API
async function loadWordPressContent() {
    const productsResponse = await fetch('https://yoursite.com/wp-json/wp/v2/products');
    const products = await productsResponse.json();
    
    // Render products
    products.forEach(product => {
        // Create product cards
    });
}
```

## 🔐 User Authentication

### 1. Firebase Auth

#### Setup
```html
<!-- Add to head -->
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js"></script>
<script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth.js"></script>
```

#### Implementation
```javascript
import { initializeApp } from 'firebase/app';
import { getAuth, signInWithEmailAndPassword, createUserWithEmailAndPassword } from 'firebase/auth';

const firebaseConfig = {
    // Your config
};

const app = initializeApp(firebaseConfig);
const auth = getAuth(app);

// Sign up
async function signUp(email, password) {
    try {
        const userCredential = await createUserWithEmailAndPassword(auth, email, password);
        console.log('User created:', userCredential.user);
    } catch (error) {
        console.error('Error:', error.message);
    }
}

// Sign in
async function signIn(email, password) {
    try {
        const userCredential = await signInWithEmailAndPassword(auth, email, password);
        console.log('User signed in:', userCredential.user);
    } catch (error) {
        console.error('Error:', error.message);
    }
}
```

### 2. Auth0 Integration

```javascript
// Initialize Auth0
const auth0 = new Auth0Client({
    domain: 'your-domain.auth0.com',
    clientId: 'your-client-id',
    redirectUri: window.location.origin
});

// Login
async function login() {
    await auth0.loginWithRedirect();
}

// Get user
async function getUser() {
    const user = await auth0.getUser();
    return user;
}
```

## 📊 Analytics Integration

### Google Analytics 4
```html
<!-- Add to head -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());
    gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

### Custom Event Tracking
```javascript
// Track product views
function trackProductView(productId, productName) {
    gtag('event', 'view_item', {
        currency: 'USD',
        value: 299,
        items: [{
            item_id: productId,
            item_name: productName,
            category: 'IoT Kits'
        }]
    });
}

// Track purchases
function trackPurchase(transactionId, value, items) {
    gtag('event', 'purchase', {
        transaction_id: transactionId,
        value: value,
        currency: 'USD',
        items: items
    });
}
```

## 🔧 Performance Optimization

### Image Optimization
```javascript
// Lazy loading implementation
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
        }
    });
});

document.querySelectorAll('img[data-src]').forEach(img => {
    imageObserver.observe(img);
});
```

### Service Worker for Caching
```javascript
// sw.js
const CACHE_NAME = 'iotech-v1';
const urlsToCache = [
    '/',
    '/assets/css/styles.css',
    '/assets/js/main.js',
    '/assets/images/logo.svg'
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => response || fetch(event.request))
    );
});
```

## 🌐 Domain & SSL Setup

### Custom Domain Configuration
1. **Purchase domain** from registrar (Namecheap, GoDaddy, etc.)
2. **Configure DNS** to point to hosting provider
3. **Set up SSL certificate** (Let's Encrypt for free)

### Cloudflare Setup (Recommended)
1. Add site to Cloudflare
2. Update nameservers at registrar
3. Configure SSL/TLS settings
4. Enable performance optimizations

## 📱 Progressive Web App (PWA)

### Manifest File
```json
{
    "name": "IoTech Academy",
    "short_name": "IoTech",
    "description": "Premium IoT Education Platform",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#0f172a",
    "theme_color": "#0ea5e9",
    "icons": [
        {
            "src": "/assets/images/icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "/assets/images/icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
```

## 🔍 SEO Optimization

### Meta Tags
```html
<!-- Add to head section -->
<meta name="description" content="Professional IoT development kits and expert tutorials">
<meta name="keywords" content="IoT, development, tutorials, hardware, education">
<meta name="author" content="IoTech Academy">

<!-- Open Graph -->
<meta property="og:title" content="IoTech Academy - Premium IoT Education">
<meta property="og:description" content="Master IoT development with professional-grade training">
<meta property="og:image" content="/assets/images/og-image.jpg">
<meta property="og:url" content="https://iotechacademy.com">

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="IoTech Academy">
<meta name="twitter:description" content="Premium IoT Education Platform">
<meta name="twitter:image" content="/assets/images/twitter-card.jpg">
```

### Structured Data
```html
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "IoTech Academy",
    "url": "https://iotechacademy.com",
    "logo": "https://iotechacademy.com/assets/images/logo.svg",
    "description": "Premium IoT education platform with professional development kits"
}
</script>
```

This deployment guide provides comprehensive options for launching and scaling your IoTech Academy website. Choose the integration options that best fit your business model and technical requirements.
