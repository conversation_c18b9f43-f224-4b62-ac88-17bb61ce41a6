<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Circuit board background -->
  <rect width="32" height="32" rx="6" fill="url(#logoGradient)" opacity="0.1"/>
  
  <!-- Main chip/processor -->
  <rect x="8" y="8" width="16" height="16" rx="2" fill="url(#logoGradient)"/>
  
  <!-- Connection points -->
  <circle cx="6" cy="10" r="1.5" fill="#0ea5e9"/>
  <circle cx="6" cy="16" r="1.5" fill="#0ea5e9"/>
  <circle cx="6" cy="22" r="1.5" fill="#0ea5e9"/>
  
  <circle cx="26" cy="10" r="1.5" fill="#f59e0b"/>
  <circle cx="26" cy="16" r="1.5" fill="#f59e0b"/>
  <circle cx="26" cy="22" r="1.5" fill="#f59e0b"/>
  
  <!-- Connection lines -->
  <line x1="7.5" y1="10" x2="8" y2="10" stroke="#0ea5e9" stroke-width="1"/>
  <line x1="7.5" y1="16" x2="8" y2="16" stroke="#0ea5e9" stroke-width="1"/>
  <line x1="7.5" y1="22" x2="8" y2="22" stroke="#0ea5e9" stroke-width="1"/>
  
  <line x1="24" y1="10" x2="24.5" y2="10" stroke="#f59e0b" stroke-width="1"/>
  <line x1="24" y1="16" x2="24.5" y2="16" stroke="#f59e0b" stroke-width="1"/>
  <line x1="24" y1="22" x2="24.5" y2="22" stroke="#f59e0b" stroke-width="1"/>
  
  <!-- IoT symbol in center -->
  <circle cx="16" cy="16" r="3" fill="none" stroke="white" stroke-width="1.5"/>
  <circle cx="16" cy="16" r="1" fill="white"/>
  
  <!-- Signal waves -->
  <path d="M 12 12 Q 16 8 20 12" fill="none" stroke="white" stroke-width="1" opacity="0.7"/>
  <path d="M 12 20 Q 16 24 20 20" fill="none" stroke="white" stroke-width="1" opacity="0.7"/>
</svg>
