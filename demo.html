<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>IoTech Academy - Demo Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .demo-container {
            max-width: 800px;
            text-align: center;
            background: rgba(30, 41, 59, 0.5);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(203, 213, 225, 0.1);
            border-radius: 1.5rem;
            padding: 3rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .logo {
            width: 64px;
            height: 64px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, #0ea5e9 0%, #f59e0b 100%);
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }
        
        h1 {
            font-size: 3rem;
            font-weight: 800;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #0ea5e9 0%, #f59e0b 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .subtitle {
            font-size: 1.25rem;
            color: #cbd5e1;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }
        
        .feature {
            background: rgba(15, 23, 42, 0.5);
            border: 1px solid rgba(203, 213, 225, 0.1);
            border-radius: 0.75rem;
            padding: 1.5rem;
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-4px);
            border-color: #0ea5e9;
        }
        
        .feature-icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .feature-title {
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .feature-desc {
            font-size: 0.875rem;
            color: #94a3b8;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 0.5rem;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1rem;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #0ea5e9 0%, #0369a1 100%);
            color: white;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
        
        .btn-outline {
            background: transparent;
            color: #cbd5e1;
            border: 1px solid #475569;
        }
        
        .btn-outline:hover {
            background: #1e293b;
            color: #f8fafc;
            border-color: #64748b;
        }
        
        .tech-stack {
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid rgba(203, 213, 225, 0.1);
        }
        
        .tech-title {
            font-size: 1.125rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #cbd5e1;
        }
        
        .tech-list {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            justify-content: center;
        }
        
        .tech-item {
            background: rgba(14, 165, 233, 0.1);
            border: 1px solid rgba(14, 165, 233, 0.3);
            color: #0ea5e9;
            padding: 0.25rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status {
            margin-top: 1.5rem;
            padding: 1rem;
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            border-radius: 0.5rem;
            color: #10b981;
            font-weight: 500;
        }
        
        @media (max-width: 768px) {
            .demo-container {
                padding: 2rem;
                margin: 1rem;
            }
            
            h1 {
                font-size: 2rem;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="logo">🚀</div>
        
        <h1>IoTech Academy</h1>
        <p class="subtitle">
            Premium IoT Education Platform - Professional showcase website for IoT development kits and expert tutorials
        </p>
        
        <div class="features">
            <div class="feature">
                <div class="feature-icon">🛠️</div>
                <div class="feature-title">Product Showcase</div>
                <div class="feature-desc">Professional IoT development kits with detailed specifications</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">📚</div>
                <div class="feature-title">Tutorial Marketplace</div>
                <div class="feature-desc">Expert-led courses with monetization features</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">🎨</div>
                <div class="feature-title">Premium Design</div>
                <div class="feature-desc">Modern, responsive design that conveys expertise</div>
            </div>
            
            <div class="feature">
                <div class="feature-icon">⚡</div>
                <div class="feature-title">Performance</div>
                <div class="feature-desc">Optimized for speed and user experience</div>
            </div>
        </div>
        
        <div class="cta-buttons">
            <a href="index.html" class="btn btn-primary">
                🌟 View Live Website
            </a>
            <a href="README.md" class="btn btn-outline">
                📖 Read Documentation
            </a>
        </div>
        
        <div class="tech-stack">
            <div class="tech-title">Built With</div>
            <div class="tech-list">
                <span class="tech-item">HTML5</span>
                <span class="tech-item">CSS3</span>
                <span class="tech-item">JavaScript</span>
                <span class="tech-item">Responsive Design</span>
                <span class="tech-item">CSS Grid</span>
                <span class="tech-item">Animations</span>
                <span class="tech-item">Performance Optimized</span>
            </div>
        </div>
        
        <div class="status">
            ✅ Website is ready for deployment and customization!
        </div>
    </div>
    
    <script>
        // Add some interactive effects
        document.querySelectorAll('.feature').forEach(feature => {
            feature.addEventListener('mouseenter', function() {
                this.style.background = 'rgba(14, 165, 233, 0.1)';
            });
            
            feature.addEventListener('mouseleave', function() {
                this.style.background = 'rgba(15, 23, 42, 0.5)';
            });
        });
        
        // Animate elements on load
        window.addEventListener('load', function() {
            const elements = document.querySelectorAll('.feature, .btn, .tech-item');
            elements.forEach((el, index) => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    el.style.transition = 'all 0.6s ease';
                    el.style.opacity = '1';
                    el.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
