<svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="faviconGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#0ea5e9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#f59e0b;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="32" height="32" rx="6" fill="url(#faviconGradient)"/>
  
  <!-- IoT symbol -->
  <circle cx="16" cy="16" r="4" fill="none" stroke="white" stroke-width="2"/>
  <circle cx="16" cy="16" r="1.5" fill="white"/>
  
  <!-- Connection nodes -->
  <circle cx="8" cy="8" r="2" fill="white" opacity="0.8"/>
  <circle cx="24" cy="8" r="2" fill="white" opacity="0.8"/>
  <circle cx="8" cy="24" r="2" fill="white" opacity="0.8"/>
  <circle cx="24" cy="24" r="2" fill="white" opacity="0.8"/>
  
  <!-- Connection lines -->
  <line x1="10" y1="10" x2="12" y2="12" stroke="white" stroke-width="1.5" opacity="0.6"/>
  <line x1="22" y1="10" x2="20" y2="12" stroke="white" stroke-width="1.5" opacity="0.6"/>
  <line x1="10" y1="22" x2="12" y2="20" stroke="white" stroke-width="1.5" opacity="0.6"/>
  <line x1="22" y1="22" x2="20" y2="20" stroke="white" stroke-width="1.5" opacity="0.6"/>
</svg>
